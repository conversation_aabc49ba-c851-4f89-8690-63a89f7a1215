In virtual_pool.rs

[H-02] Freeze Authority Enabled on Base Mint Prevents Raydium Migration
Description
The base_mint is set during initialization without validating that its freeze authority is disabled. As per Raydium documentation (noted in Audits C [C-03], D/E [M-03], [M-04]), tokens with enabled freeze authority cannot be used in liquidity pools, blocking migration. This is unchecked in initialize, allowing pools with frozen mints to be created, rendering migration impossible.
Impact
Permanent DoS on migration, locking all collected funds (e.g., quote reserves) and halting protocol functionality, leading to total loss for users and creators.

Affected Code
```rust
pub fn initialize(
    &mut self,
    // ...
    base_mint: Pubkey,
    // ...
) {
    // No check for mint.freeze_authority.is_none()
    self.base_mint = base_mint;
}
```
Reference: initialize.